<template>
  <div ref="tooltipRef" class="status-tooltip" :style="tooltipStyle">
    <template v-for="(section, sectionIndex) in sections" :key="sectionIndex">
      <div
        v-for="(row, rowIndex) in section.filter(
          ({ value }) => value !== undefined && value !== null,
        )"
        :key="rowIndex"
        class="tooltip-row"
      >
        <span class="tooltip-label">{{ row.label }}:</span>
        <span class="tooltip-value">{{ row.value }}</span>
      </div>
      <div v-if="sectionIndex < sections.length - 1" class="tooltip-divider"></div>
    </template>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed } from 'vue'

type Row = { label: string; value: string | number | null }
type Section = Row[]

const props = defineProps<{
  sections: Section[]
  targetElement?: HTMLElement
}>()

const tooltipRef = ref<HTMLElement>()
const position = ref({ top: 0, left: 0 })

const updatePosition = () => {
  if (!props.targetElement || !tooltipRef.value) return

  const targetRect = props.targetElement.getBoundingClientRect()
  const tooltipRect = tooltipRef.value.getBoundingClientRect()

  // Calculate available space above and below
  const spaceAbove = targetRect.top
  const spaceBelow = window.innerHeight - targetRect.bottom

  // Determine if tooltip should be above or below
  const showAbove = spaceAbove >= tooltipRect.height + 8 || spaceAbove > spaceBelow

  // Calculate position
  let top
  if (showAbove) {
    // Position above the target element
    top = targetRect.top - tooltipRect.height - 8 // 8px margin
  } else {
    // Position below the target element
    top = targetRect.bottom + 8 // 8px margin
  }

  const left = targetRect.left + targetRect.width / 2 - tooltipRect.width / 2

  // Ensure tooltip stays within viewport
  const viewportWidth = window.innerWidth
  const adjustedLeft = Math.max(8, Math.min(left, viewportWidth - tooltipRect.width - 8))

  position.value = {
    top: Math.max(8, top),
    left: adjustedLeft,
  }
}

onMounted(() => {
  if (props.targetElement) {
    updatePosition()
    window.addEventListener('scroll', updatePosition)
    window.addEventListener('resize', updatePosition)
  }
})

onUnmounted(() => {
  window.removeEventListener('scroll', updatePosition)
  window.removeEventListener('resize', updatePosition)
})

const tooltipStyle = computed(() => {
  if (!props.targetElement) {
    return {}
  }

  return {
    position: 'fixed' as const,
    top: `${position.value.top}px`,
    left: `${position.value.left}px`,
    transform: 'none',
    bottom: 'auto',
    marginBottom: '0',
  }
})
</script>

<style scoped>
.status-tooltip {
  position: absolute;
  background-color: rgb(0 0 0 / 85%);
  color: white;
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 14px;
  z-index: var(--z-index-status-tooltip);
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  pointer-events: none;
  min-width: 180px;
  width: max-content;
  max-width: 400px;
  box-shadow: 0 2px 10px rgb(0 0 0 / 20%);
  margin-bottom: 8px;
}

.tooltip-row {
  display: flex;
  align-items: flex-start;
  margin-bottom: 4px;
  white-space: nowrap;
  overflow: hidden;
}

.tooltip-row:last-child {
  margin-bottom: 0;
}

.tooltip-label {
  color: rgb(255 255 255 / 80%);
  font-weight: 500;
  margin-right: 8px;
  flex-shrink: 0;
  min-width: 100px;
  text-align: left;
}

.tooltip-value {
  font-weight: 500;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  flex: 1;
  text-align: left;
}

.tooltip-section {
  margin-top: 8px;
}

.tooltip-divider {
  height: 1px;
  background-color: rgb(255 255 255 / 20%);
  margin: 8px 0;
}
</style>
